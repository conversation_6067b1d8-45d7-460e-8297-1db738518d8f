/**
 * Utility functions for User Initiated Linking (UIL) flow
 */

import { db } from "@/lib/db";
import { generateUUID } from "../utils/request";
import { UILOtpData } from "@/types/abdm/user-initiated-linking";

/**
 * Generate a 6-digit OTP
 * @returns 6-digit OTP
 */
export function generateOTP(): string {
  // Generate a random 6-digit OTP for production use
  const min = 100000; // Minimum 6-digit number
  const max = 999999; // Maximum 6-digit number
  return Math.floor(Math.random() * (max - min + 1) + min).toString();
}

/**
 * Generate a link reference number (UUID)
 * @returns UUID
 */
export function generateLinkRefNumber(): string {
  return generateUUID();
}

/**
 * Store OTP data in the database
 * @param otpData - OTP data to store
 * @returns Stored OTP data
 */
export async function storeOTP(
  otpData: Omit<UILOtpData, "verified">,
): Promise<UILOtpData> {
  try {
    // Store OTP in the database
    const result = await db.uILOtpNotify.create({
      data: {
        linkRefNumber: otpData.linkRefNumber,
        otp: otpData.otp,
        transactionId: otpData.transactionId,
        patientId: otpData.patientId,
        careContexts: otpData.careContexts,
        expiresAt: otpData.expiresAt,
        verified: false,
        smsDeliveryStatus: "pending",
      },
    });

    return {
      ...result,
      careContexts: result.careContexts as any,
      abhaAddress: undefined,
    };
  } catch (error) {
    console.error("Error storing OTP:", error);
    throw error;
  }
}

/**
 * Validate OTP
 * @param linkRefNumber - Link reference number
 * @param otp - OTP to validate
 * @returns Boolean indicating if OTP is valid
 */
export async function validateOTP(
  linkRefNumber: string,
  otp: string,
): Promise<boolean> {
  try {
    // Get OTP data from the database
    const otpData = await db.uILOtpNotify.findUnique({
      where: {
        linkRefNumber,
      },
    });

    // Check if OTP data exists
    if (!otpData) {
      return false;
    }

    // Check if OTP is expired
    if (otpData.expiresAt < new Date()) {
      return false;
    }

    // Check if OTP is already verified
    if (otpData.verified) {
      return false;
    }

    // Check if OTP matches
    return otpData.otp === otp;
  } catch (error) {
    console.error("Error validating OTP:", error);
    return false;
  }
}

/**
 * Mark OTP as verified
 * @param linkRefNumber - Link reference number
 * @returns Boolean indicating if OTP was marked as verified
 */
export async function markOTPAsVerified(
  linkRefNumber: string,
): Promise<boolean> {
  try {
    // Update OTP data in the database
    const result = await db.uILOtpNotify.update({
      where: {
        linkRefNumber,
      },
      data: {
        verified: true,
      },
    });

    return result.verified;
  } catch (error) {
    console.error("Error marking OTP as verified:", error);
    return false;
  }
}

/**
 * Get OTP data by link reference number
 * @param linkRefNumber - Link reference number
 * @returns OTP data
 */
export async function getOTPData(
  linkRefNumber: string,
): Promise<UILOtpData | null> {
  try {
    // Get OTP data from the database
    const otpData = await db.uILOtpNotify.findUnique({
      where: {
        linkRefNumber,
      },
    });

    if (!otpData) {
      return null;
    }

    return {
      ...otpData,
      careContexts: otpData.careContexts as any,
      abhaAddress: undefined,
    };
  } catch (error) {
    console.error("Error getting OTP data:", error);
    return null;
  }
}

/**
 * Update OTP data with request ID
 * @param linkRefNumber - Link reference number
 * @param requestId - Request ID
 * @returns Updated OTP data
 */
export async function updateOTPWithRequestId(
  linkRefNumber: string,
  requestId: string,
): Promise<UILOtpData | null> {
  try {
    // Update OTP data in the database
    const result = await db.uILOtpNotify.update({
      where: {
        linkRefNumber,
      },
      data: {
        requestId,
      },
    });

    return {
      ...result,
      careContexts: result.careContexts as any,
      abhaAddress: undefined,
    };
  } catch (error) {
    console.error("Error updating OTP with request ID:", error);
    return null;
  }
}
