export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";

// GET /api/prescriptions/[id] - Get a specific prescription
export async function GET(_req: NextRequest, { params }: any) {
  try {
    const { id } = params;

    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the prescription
    const prescription = await db.prescription.findUnique({
      where: {
        id,
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            dateOfBirth: true,
            gender: true,
            phone: true,
            email: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
          },
        },
        consultation: {
          select: {
            id: true,
            consultationDate: true,
            status: true,
          },
        },
        items: true,
      },
    });

    if (!prescription) {
      return NextResponse.json(
        { error: "Prescription not found" },
        { status: 404 },
      );
    }

    // If the user is a doctor, check if they are the doctor for this prescription
    if (user.role === "doctor") {
      const doctor = await db.doctor.findFirst({
        where: {
          userId: user.id,
        },
      });

      if (!doctor || doctor.id !== prescription.doctorId) {
        return NextResponse.json(
          {
            error:
              "Forbidden: You are not authorized to view this prescription",
          },
          { status: 403 },
        );
      }
    }

    return NextResponse.json({ prescription });
  } catch (error) {
    console.error("Error fetching prescription:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

// PUT /api/prescriptions/[id] - Update a prescription
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the prescription data from the request
    const {
      // validUntil,
      // instructions,
      // status,
      items,
    } = await req.json();

    // Check if the prescription exists
    const existingPrescription = await db.prescription.findUnique({
      where: {
        id,
      },
      include: {
        items: true,
      },
    });

    if (!existingPrescription) {
      return NextResponse.json(
        { error: "Prescription not found" },
        { status: 404 },
      );
    }

    // If the user is a doctor, check if they are the doctor for this prescription
    if (user.role === "doctor") {
      const doctor = await db.doctor.findFirst({
        where: {
          userId: user.id,
        },
      });

      if (!doctor || doctor.id !== existingPrescription.doctorId) {
        return NextResponse.json(
          {
            error:
              "Forbidden: You are not authorized to update this prescription",
          },
          { status: 403 },
        );
      }
    }

    // Update the prescription
    const updatedPrescription = await db.$transaction(async () => {
      // Update the prescription
      // const prescription = await db.prescription.update({
      //   where: {
      //     id,
      //   },
      //   data: {
      //     validUntil: validUntil ? new Date(validUntil) : null,
      //     instructions,
      //     status,
      //   },
      // });

      // If items are provided, update them
      if (items && items.length > 0) {
        // Delete existing items
        await db.prescriptionItem.deleteMany({
          where: {
            prescriptionId: id,
          },
        });

        // Create new items
        await db.prescriptionItem.createMany({
          data: items.map((item: any) => ({
            prescriptionId: id,
            medicationName: item.medicationName,
            dosage: item.dosage,
            frequency: item.timing || item.frequency || "1-1-D", // Map timing to frequency for database
            duration: item.duration,
            route: item.route,
            method: item.method && item.method.trim() ? item.method : null, // Add method field
            instructions: item.instructions,
            reason: item.reason && item.reason.trim() ? item.reason : null, // Add reason field
            snomedCode: item.snomedCode,
            rxcui: item.rxcui,
            rxNormData: item.rxNormData ? JSON.stringify(item.rxNormData) : null,
          })),
        });
      }

      // Return the updated prescription with items
      return await db.prescription.findUnique({
        where: {
          id,
        },
        include: {
          patient: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              dateOfBirth: true,
              gender: true,
            },
          },
          doctor: {
            select: {
              id: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              specialization: true,
            },
          },
          consultation: {
            select: {
              id: true,
              consultationDate: true,
              status: true,
            },
          },
          items: true,
        },
      });
    });

    // Automatically regenerate and store FHIR bundles for updated prescription using correct system
    if (updatedPrescription) {
      try {
        const { generateAllBundles } = await import(
          "@/services/fhir/generate-bundles"
        );
        const { storeFhirBundles } = await import("@/lib/fhir-bundle-storage");

        const bundlesResult = await generateAllBundles(
          updatedPrescription.consultationId,
          updatedPrescription.organizationId,
        );

        if (bundlesResult.success && bundlesResult.prescription) {
          // Store only the prescription bundle since prescription was updated
          const bundlesToStore = {
            prescription: bundlesResult.prescription,
          };

          const storedBundles = await storeFhirBundles(
            bundlesToStore,
            updatedPrescription.consultationId,
            updatedPrescription.patientId,
            updatedPrescription.organizationId,
          );

          console.log(
            "✅ Regenerated FHIR prescription bundle for updated prescription",
            {
              consultationId: updatedPrescription.consultationId,
              bundleCount: storedBundles.length,
              bundleTypes: storedBundles.map((b) => b.bundleType),
            },
          );
        }
      } catch (bundleError) {
        console.error(
          "❌ Failed to regenerate FHIR bundles for updated prescription",
          {
            consultationId: updatedPrescription.consultationId,
            error:
              bundleError instanceof Error
                ? bundleError.message
                : String(bundleError),
          },
        );
        // Don't fail the prescription update if bundle generation fails
      }
    }

    return NextResponse.json({
      prescription: updatedPrescription,
      message: "Prescription updated successfully",
    });
  } catch (error) {
    console.error("Error updating prescription:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}

// DELETE /api/prescriptions/[id] - Delete a prescription
export async function DELETE(
  _req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const { id } = params;

    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if the prescription exists
    const existingPrescription = await db.prescription.findUnique({
      where: {
        id,
      },
    });

    if (!existingPrescription) {
      return NextResponse.json(
        { error: "Prescription not found" },
        { status: 404 },
      );
    }

    // If the user is a doctor, check if they are the doctor for this prescription
    if (user.role === "doctor") {
      const doctor = await db.doctor.findFirst({
        where: {
          userId: user.id,
        },
      });

      if (!doctor || doctor.id !== existingPrescription.doctorId) {
        return NextResponse.json(
          {
            error:
              "Forbidden: You are not authorized to delete this prescription",
          },
          { status: 403 },
        );
      }
    }

    // Delete the prescription (cascade will delete items)
    await db.prescription.delete({
      where: {
        id,
      },
    });

    return NextResponse.json({
      message: "Prescription deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting prescription:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
