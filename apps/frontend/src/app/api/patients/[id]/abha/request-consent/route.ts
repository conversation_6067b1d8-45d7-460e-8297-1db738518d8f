export const maxDuration = 299;

import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";

// POST /api/patients/[id]/abha/request-consent - Request consent for a patient
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Get the current user from cookies
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const patientId = params.id;

    // Get the patient's ABHA profile
    const patient = await db.patient.findUnique({
      where: {
        id: patientId,
      },
      include: {
        abhaProfile: true,
      },
    });

    if (!patient || !patient.abhaProfile) {
      return NextResponse.json(
        { error: "Patient does not have an ABHA profile" },
        { status: 404 },
      );
    }

    // Check if the patient has an active ABHA session (xToken)
    const hasActiveSession = Boolean(
      patient.abhaProfile.xToken &&
        patient.abhaProfile.xTokenExpiresAt &&
        new Date() < new Date(patient.abhaProfile.xTokenExpiresAt),
    );

    if (!hasActiveSession) {
      return NextResponse.json(
        { error: "Patient does not have an active ABHA session" },
        { status: 401 },
      );
    }

    // Parse request body
    const { purpose, hiTypes, dateRange } = await req.json();

    // Validate required fields
    if (!purpose || !hiTypes || !dateRange) {
      return NextResponse.json(
        { error: "Purpose, hiTypes, and dateRange are required" },
        { status: 400 },
      );
    }

    // In a real implementation, you would call the ABDM API to request consent
    // For now, we'll create a mock consent record
    const consent = await db.consent.create({
      data: {
        patientId,
        purpose,
        hiTypes: hiTypes,
        requestedHiTypes: hiTypes, // Store originally requested HI types - this should never be updated
        consentRequestId: `request-${Date.now()}`,
        consentId: `consent-${Date.now()}`,
        status: "GRANTED", // For demo purposes, we'll assume consent is granted immediately
        permission: {
          dateRange: {
            from: new Date(dateRange.from),
            to: new Date(dateRange.to),
          },
          accessMode: "VIEW",
        },
        organizationId: patient.organizationId,
      },
    });

    return NextResponse.json({
      consentId: consent.consentId,
      status: consent.status,
      message: "Consent request processed successfully",
    });
  } catch (error) {
    console.error("Error requesting consent:", error);
    return NextResponse.json(
      { error: "Something went wrong" },
      { status: 500 },
    );
  }
}
